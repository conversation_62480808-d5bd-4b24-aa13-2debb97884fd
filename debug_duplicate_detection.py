#!/usr/bin/env python3
"""
Debug script to identify false positives in duplicate detection logic.
"""

import sys
import os
import tempfile

# Add the current directory to the path so we can import main
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_duplicate_detection():
    """Debug the duplicate detection logic with specific test cases."""
    print("Debugging duplicate detection false positives...")
    
    from main import ScreenTextTyper
    
    class TestTyper(ScreenTextTyper):
        def __init__(self):
            # Initialize required attributes for testing
            self.temp_file_path = None
            self.temp_file_created = False
    
    app = TestTyper()
    
    # Test cases that should NOT be detected as duplicates
    false_positive_cases = [
        {
            'description': 'Completely different content',
            'previous': 'This is about programming in Python',
            'current': 'Today we will discuss cooking recipes',
            'should_be_duplicate': False
        },
        {
            'description': 'Different topics with some common words',
            'previous': 'The quick brown fox jumps over the lazy dog',
            'current': 'The lazy cat sleeps under the warm sun',
            'should_be_duplicate': False
        },
        {
            'description': 'Similar structure but different content',
            'previous': 'Chapter 1: Introduction to Machine Learning\nThis chapter covers basic concepts',
            'current': 'Chapter 2: Advanced Data Structures\nThis chapter covers complex algorithms',
            'should_be_duplicate': False
        },
        {
            'description': 'Different documents with common phrases',
            'previous': 'In this tutorial, we will learn about databases',
            'current': 'In this guide, we will explore web development',
            'should_be_duplicate': False
        },
        {
            'description': 'Unrelated content with minimal overlap',
            'previous': 'JavaScript is a programming language for web development',
            'current': 'Cooking pasta requires boiling water and adding salt',
            'should_be_duplicate': False
        }
    ]
    
    # Test cases that SHOULD be detected as duplicates
    true_positive_cases = [
        {
            'description': 'Exact duplicate content',
            'previous': 'This is the exact same text\nWith multiple lines',
            'current': 'This is the exact same text\nWith multiple lines',
            'should_be_duplicate': True
        },
        {
            'description': 'Content with additional text at end',
            'previous': 'This is the beginning of a document\nWith some content',
            'current': 'This is the beginning of a document\nWith some content\nAnd some new text',
            'should_be_duplicate': True
        }
    ]
    
    print("=== Testing FALSE POSITIVE cases (should NOT be duplicates) ===\n")
    
    for i, case in enumerate(false_positive_cases, 1):
        print(f"Test {i}: {case['description']}")
        print(f"Previous: '{case['previous']}'")
        print(f"Current: '{case['current']}'")
        
        # Test cross-capture duplicate detection
        duplicates, resume_pos, skipped = app.detect_cross_capture_duplicates(case['previous'], case['current'])
        print(f"Cross-capture duplicates: {duplicates}, resume_pos: {resume_pos}")
        
        # Test content overlap detection
        overlap = app.detect_content_overlap(case['previous'], case['current'])
        print(f"Content overlap detected: {overlap}")
        
        # Test is_content_already_typed (requires temp file)
        temp_fd, temp_path = tempfile.mkstemp(prefix='autotyper_debug_', suffix='.txt')
        app.temp_file_path = temp_path
        app.temp_file_created = True
        
        try:
            with os.fdopen(temp_fd, 'w') as f:
                f.write(case['previous'])
            
            already_typed = app.is_content_already_typed(case['current'])
            print(f"Content already typed: {already_typed}")
            
            # Check if this is a false positive
            if already_typed and not case['should_be_duplicate']:
                print("❌ FALSE POSITIVE: Content marked as already typed when it shouldn't be!")
            elif not already_typed and case['should_be_duplicate']:
                print("❌ FALSE NEGATIVE: Content not marked as already typed when it should be!")
            else:
                print("✅ CORRECT: Detection result matches expectation")
                
        finally:
            if os.path.exists(temp_path):
                os.remove(temp_path)
        
        print("-" * 60)
    
    print("\n=== Testing TRUE POSITIVE cases (should BE duplicates) ===\n")
    
    for i, case in enumerate(true_positive_cases, 1):
        print(f"Test {i}: {case['description']}")
        print(f"Previous: '{case['previous']}'")
        print(f"Current: '{case['current']}'")
        
        # Test cross-capture duplicate detection
        duplicates, resume_pos, skipped = app.detect_cross_capture_duplicates(case['previous'], case['current'])
        print(f"Cross-capture duplicates: {duplicates}, resume_pos: {resume_pos}")
        
        # Test content overlap detection
        overlap = app.detect_content_overlap(case['previous'], case['current'])
        print(f"Content overlap detected: {overlap}")
        
        # Test is_content_already_typed (requires temp file)
        temp_fd, temp_path = tempfile.mkstemp(prefix='autotyper_debug_', suffix='.txt')
        app.temp_file_path = temp_path
        app.temp_file_created = True
        
        try:
            with os.fdopen(temp_fd, 'w') as f:
                f.write(case['previous'])
            
            already_typed = app.is_content_already_typed(case['current'])
            print(f"Content already typed: {already_typed}")
            
            # Check if this is correct
            if already_typed and case['should_be_duplicate']:
                print("✅ CORRECT: Content correctly marked as already typed")
            elif not already_typed and not case['should_be_duplicate']:
                print("✅ CORRECT: Content correctly not marked as already typed")
            else:
                print("❌ INCORRECT: Detection result doesn't match expectation")
                
        finally:
            if os.path.exists(temp_path):
                os.remove(temp_path)
        
        print("-" * 60)

def debug_word_overlap_thresholds():
    """Debug the word overlap threshold logic specifically."""
    print("\n=== Debugging Word Overlap Thresholds ===\n")
    
    from main import ScreenTextTyper
    
    class TestTyper(ScreenTextTyper):
        def __init__(self):
            pass
    
    app = TestTyper()
    
    # Test cases with different overlap percentages
    overlap_test_cases = [
        {
            'typed': 'The quick brown fox jumps over the lazy dog',
            'current': 'The lazy cat sleeps under the warm sun',
            'description': 'Low meaningful overlap (should be False)'
        },
        {
            'typed': 'Python programming language tutorial for beginners',
            'current': 'Java programming language guide for experts',
            'description': 'Medium meaningful overlap (borderline case)'
        },
        {
            'typed': 'Machine learning algorithms and data science techniques',
            'current': 'Machine learning models and data science methods',
            'description': 'High meaningful overlap (should be True)'
        },
        {
            'typed': 'This is a test with common words',
            'current': 'That was a check with common terms',
            'description': 'Only stop words overlap (should be False)'
        }
    ]
    
    for case in overlap_test_cases:
        print(f"Test: {case['description']}")
        print(f"Typed: '{case['typed']}'")
        print(f"Current: '{case['current']}'")
        
        # Manually calculate what the function does
        common_stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'this', 'that', 'these', 'those', 'it', 'its', 'they', 'them', 'their'}
        
        typed_words = set(word for word in case['typed'].lower().split() if len(word) > 2 and word not in common_stop_words)
        current_words = set(word for word in case['current'].lower().split() if len(word) > 2 and word not in common_stop_words)
        
        print(f"Typed words (filtered): {typed_words}")
        print(f"Current words (filtered): {current_words}")
        
        if typed_words and current_words:
            common_words = typed_words.intersection(current_words)
            overlap_percentage = len(common_words) / min(len(typed_words), len(current_words))
            
            print(f"Common words: {common_words}")
            print(f"Overlap percentage: {overlap_percentage:.2f}")
            print(f"Threshold (0.5): {'PASS' if overlap_percentage > 0.5 else 'FAIL'}")
        
        result = app.detect_content_overlap(case['typed'], case['current'])
        print(f"Function result: {result}")
        print("-" * 50)

if __name__ == "__main__":
    try:
        debug_duplicate_detection()
        debug_word_overlap_thresholds()
        
    except Exception as e:
        print(f"Debug failed with error: {e}")
        import traceback
        traceback.print_exc()
