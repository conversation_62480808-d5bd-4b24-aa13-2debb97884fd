#!/usr/bin/env python3
"""
Debug script to test if text normalization is causing false positives in duplicate detection.
"""

import sys
import os
import tempfile

# Add the current directory to the path so we can import main
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_normalization_impact():
    """Debug if text normalization is causing false positives."""
    print("Debugging text normalization impact on duplicate detection...")
    
    from main import ScreenTextTyper
    
    class TestTyper(ScreenTextTyper):
        def __init__(self):
            # Initialize required attributes for testing
            self.temp_file_path = None
            self.temp_file_created = False
            self.cursor_removal_enabled = True
            self.spell_checker = None
            self.cursor_artifacts_removed = 0
            self.last_cursor_removal_time = 0
    
    app = TestTyper()
    
    # Test cases with raw OCR-like text that might be normalized differently
    normalization_test_cases = [
        {
            'description': 'Different content with similar structure after normalization',
            'raw_previous': 'Chapter 1: Intro\nduction to\nPython Programming',
            'raw_current': 'Chapter 2: Intro\nduction to\nJava Programming',
            'should_be_duplicate': False
        },
        {
            'description': 'Different content with word wrapping that might normalize similarly',
            'raw_previous': 'The qu\nick brown\nfox jumps',
            'raw_current': 'The la\nzy cat\nsleeps well',
            'should_be_duplicate': False
        },
        {
            'description': 'Similar content with different word breaks',
            'raw_previous': 'Machine lear\nning algorithms\nfor data science',
            'raw_current': 'Machine learn\ning algorithms\nfor data science',
            'should_be_duplicate': True
        },
        {
            'description': 'Completely different topics that might share common normalized words',
            'raw_previous': 'Cooking rec\nipes for\nbeginners',
            'raw_current': 'Programming tut\norials for\nbeginners',
            'should_be_duplicate': False
        },
        {
            'description': 'Same content with different OCR artifacts',
            'raw_previous': 'Hello world|\nThis is a test',
            'raw_current': 'Hello world\nThis is a test',
            'should_be_duplicate': True
        }
    ]
    
    print("=== Testing normalization impact on duplicate detection ===\n")
    
    for i, case in enumerate(normalization_test_cases, 1):
        print(f"Test {i}: {case['description']}")
        print(f"Raw Previous: '{case['raw_previous']}'")
        print(f"Raw Current: '{case['raw_current']}'")
        
        # Normalize both texts
        normalized_previous = app.normalize_text(case['raw_previous'])
        normalized_current = app.normalize_text(case['raw_current'])
        
        print(f"Normalized Previous: '{normalized_previous}'")
        print(f"Normalized Current: '{normalized_current}'")
        
        # Test duplicate detection on raw text
        raw_duplicates, raw_resume_pos, _ = app.detect_cross_capture_duplicates(
            case['raw_previous'], case['raw_current']
        )
        print(f"Raw text duplicates: {raw_duplicates}, resume_pos: {raw_resume_pos}")
        
        # Test duplicate detection on normalized text
        norm_duplicates, norm_resume_pos, _ = app.detect_cross_capture_duplicates(
            normalized_previous, normalized_current
        )
        print(f"Normalized text duplicates: {norm_duplicates}, resume_pos: {norm_resume_pos}")
        
        # Test content overlap on normalized text
        overlap = app.detect_content_overlap(normalized_previous, normalized_current)
        print(f"Content overlap detected: {overlap}")
        
        # Test full pipeline with temp file
        temp_fd, temp_path = tempfile.mkstemp(prefix='autotyper_debug_', suffix='.txt')
        app.temp_file_path = temp_path
        app.temp_file_created = True
        
        try:
            # Save normalized previous text to temp file (simulating real usage)
            with os.fdopen(temp_fd, 'w') as f:
                f.write(normalized_previous)
            
            # Test if normalized current text is considered already typed
            already_typed = app.is_content_already_typed(normalized_current)
            print(f"Content already typed: {already_typed}")
            
            # Check if this is correct
            if already_typed and not case['should_be_duplicate']:
                print("❌ FALSE POSITIVE: Normalization caused different content to be marked as duplicate!")
                print(f"   This suggests the normalization process is too aggressive")
            elif not already_typed and case['should_be_duplicate']:
                print("❌ FALSE NEGATIVE: Similar content not detected as duplicate")
            else:
                print("✅ CORRECT: Detection result matches expectation")
                
        finally:
            if os.path.exists(temp_path):
                os.remove(temp_path)
        
        print("-" * 70)

def debug_specific_normalization_steps():
    """Debug each step of the normalization process."""
    print("\n=== Debugging specific normalization steps ===\n")
    
    from main import ScreenTextTyper
    
    class TestTyper(ScreenTextTyper):
        def __init__(self):
            self.cursor_removal_enabled = True
            self.spell_checker = None
            self.cursor_artifacts_removed = 0
            self.last_cursor_removal_time = 0
    
    app = TestTyper()
    
    # Test case that might be problematic
    test_texts = [
        'Chapter 1: Introduction to Python Programming',
        'Chapter 2: Introduction to Java Programming',
        'The quick brown fox jumps over the lazy dog',
        'The lazy cat sleeps under the warm sun'
    ]
    
    for text in test_texts:
        print(f"Original: '{text}'")
        
        # Step 1: Cursor artifact removal
        step1 = app.remove_cursor_artifacts(text)
        print(f"After cursor removal: '{step1}'")
        
        # Step 2: Full normalization
        step2 = app.normalize_text(text)
        print(f"After full normalization: '{step2}'")
        
        print("-" * 50)

def debug_line_matching_criteria():
    """Debug the line matching criteria in cross-capture duplicate detection."""
    print("\n=== Debugging line matching criteria ===\n")
    
    from main import ScreenTextTyper
    
    class TestTyper(ScreenTextTyper):
        def __init__(self):
            pass
    
    app = TestTyper()
    
    # Test problematic line pairs
    line_pairs = [
        ('Chapter 1: Introduction to Python', 'Chapter 2: Introduction to Java'),
        ('This is a test line', 'This is a test line'),
        ('Programming tutorials for beginners', 'Cooking recipes for beginners'),
        ('The quick brown fox', 'The lazy cat sleeps'),
        ('Machine learning algorithms', 'Machine learning models'),
    ]
    
    for prev_line, curr_line in line_pairs:
        print(f"Previous line: '{prev_line}'")
        print(f"Current line: '{curr_line}'")
        
        # Check the matching criteria used in detect_cross_capture_duplicates
        prev_stripped = prev_line.strip()
        curr_stripped = curr_line.strip()
        
        identical = prev_stripped == curr_stripped
        multi_word_prev = len(prev_stripped.split()) > 1
        multi_word_curr = len(curr_stripped.split()) > 1
        non_empty = len(prev_stripped) > 0
        
        would_match = identical and multi_word_prev and non_empty
        
        print(f"  Identical: {identical}")
        print(f"  Multi-word (prev): {multi_word_prev}")
        print(f"  Multi-word (curr): {multi_word_curr}")
        print(f"  Non-empty: {non_empty}")
        print(f"  Would match: {would_match}")
        print("-" * 40)

if __name__ == "__main__":
    try:
        debug_normalization_impact()
        debug_specific_normalization_steps()
        debug_line_matching_criteria()
        
    except Exception as e:
        print(f"Debug failed with error: {e}")
        import traceback
        traceback.print_exc()
