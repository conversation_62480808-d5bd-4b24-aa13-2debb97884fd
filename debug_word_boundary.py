#!/usr/bin/env python3
"""
Debug script to understand why "good morning" is still being joined.
"""

import sys
import os

# Add the current directory to the path so we can import main
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_word_boundary_decision():
    """Debug the word boundary decision for specific cases."""
    print("Debugging word boundary decision logic...")
    
    from main import ScreenTextTyper
    
    class TestTyper(ScreenTextTyper):
        def __init__(self):
            # Initialize required attributes for testing
            self.cursor_removal_enabled = True
            self.spell_checker = None  # We'll rely on built-in dictionaries
    
    app = TestTyper()
    
    # Test the problematic cases
    test_cases = [
        ('good', 'morning'),
        ('a', 'car'),
        ('yah', 'oo'),
        ('c', 'loset'),
    ]
    
    for last_word, first_word in test_cases:
        print(f"\n=== Debugging: '{last_word}' + '{first_word}' ===")
        
        # Check each step of the decision process
        print(f"1. is_complete_common_word('{last_word}'): {app.is_complete_common_word(last_word)}")
        print(f"2. is_complete_common_word('{first_word}'): {app.is_complete_common_word(first_word)}")
        print(f"3. is_valid_word('{last_word}'): {app.is_valid_word(last_word)}")
        print(f"4. is_valid_word('{first_word}'): {app.is_valid_word(first_word)}")
        print(f"5. is_valid_word('{last_word + first_word}'): {app.is_valid_word(last_word + first_word)}")
        
        # Check individual decision functions
        print(f"6. is_likely_word_continuation: {app.is_likely_word_continuation(last_word, first_word)}")
        print(f"7. is_likely_compound_word: {app.is_likely_compound_word(last_word, first_word)}")
        print(f"8. suggests_line_wrap: {app.suggests_line_wrap(last_word, first_word)}")
        print(f"9. suggests_separate_words: {app.suggests_separate_words(last_word, first_word)}")
        
        # Final decision
        print(f"10. analyze_word_boundary: {app.analyze_word_boundary(last_word, first_word)}")
        print(f"11. is_word_wrapped_with_validation: {app.is_word_wrapped_with_validation(last_word, first_word)}")

if __name__ == "__main__":
    debug_word_boundary_decision()
