#!/usr/bin/env python3
"""
Test script to verify the current behavior of the autotyper with the latest fixes.
"""

import tempfile
import os
from unittest.mock import Mock

def test_current_typing_behavior():
    """Test the current typing behavior to identify any remaining issues."""
    print("Testing Current Typing Behavior")
    print("="*50)
    
    # Import the main module
    from main import ScreenTextTyper
    
    # Create a test instance with minimal setup
    class TestTyper(ScreenTextTyper):
        def __init__(self):
            # Initialize only the necessary attributes for testing
            self.delay = 0.1
            self.error_rate = 0.05
            self.variance = 0.1
            self.text_stability_threshold = 1.0
            
            # Enhanced state tracking for incremental updates
            self.current_text = ""
            self.previous_text = ""
            self.typed_text = ""
            self.cursor_position = 0
            from collections import deque
            self.text_history = deque(maxlen=10)
            self.typing_active = False
            self.capture_active = False
            
            # Trailing space feature state tracking
            self.last_typing_completion_time = 0
            self.text_stable_since = 0
            self.trailing_space_added = False
            self.last_stable_text = ""
            
            # Duplicate line detection state tracking
            self.duplicate_lines_skipped = 0
            self.total_lines_processed = 0
            self.last_duplicate_detection_time = 0
            
            # Cursor artifact removal state tracking
            self.cursor_artifacts_removed = 0
            self.cursor_removal_enabled = True
            self.last_cursor_removal_time = 0
            
            # Persistent text tracking state
            self.temp_file_path = None
            self.cross_capture_duplicates_skipped = 0
            self.last_cross_capture_detection_time = 0
            self.temp_file_created = False
            
            # Mock threading lock
            self.lock = Mock()
            
            # Track typing calls for testing
            self.typing_calls = []
            
        def type_text_with_effects(self, text):
            """Mock typing method that records calls instead of actually typing."""
            self.typing_calls.append(text)
            print(f"MOCK TYPING: '{text[:50]}...'")
            
        def position_cursor_at_end(self):
            """Mock cursor positioning."""
            print(f"MOCK CURSOR POSITIONING: Moving to position {len(self.typed_text)}")
            
        def position_cursor_at(self, position):
            """Mock cursor positioning."""
            print(f"MOCK CURSOR POSITIONING: Moving to position {position}")
    
    # Create test instance
    typer = TestTyper()
    
    print("\n=== Test 1: Initial typing (should work) ===")
    typer.typed_text = ""
    typer.current_text = "Hello, this is the first paragraph."
    typer.previous_text = ""
    typer.typing_calls.clear()
    
    print(f"Typed text: '{typer.typed_text}'")
    print(f"Current text: '{typer.current_text}'")
    
    typer.process_text_changes()
    
    if typer.typing_calls:
        print(f"✓ SUCCESS: Initial typing worked - '{typer.typing_calls[0][:50]}...'")
    else:
        print(f"✗ FAILURE: Initial typing failed")
    
    print("\n=== Test 2: Completely different content (should type) ===")
    typer.typed_text = "Hello, this is the first paragraph."
    typer.current_text = "This is completely different content."
    typer.previous_text = ""
    typer.typing_calls.clear()
    
    print(f"Typed text: '{typer.typed_text[:50]}...'")
    print(f"Current text: '{typer.current_text[:50]}...'")
    
    typer.process_text_changes()
    
    if typer.typing_calls:
        print(f"✓ SUCCESS: Different content typing worked - '{typer.typing_calls[0][:50]}...'")
    else:
        print(f"✗ FAILURE: Different content typing failed")
    
    print("\n=== Test 3: Content with temp file (should check for duplicates) ===")
    # Create a temp file with some content
    temp_fd, temp_path = tempfile.mkstemp(prefix='autotyper_test_', suffix='.txt')
    typer.temp_file_path = temp_path
    typer.temp_file_created = True
    
    try:
        with os.fdopen(temp_fd, 'w') as f:
            f.write("This is completely different content.")
        
        typer.typed_text = "Hello, this is the first paragraph."
        typer.current_text = "This is completely different content."  # Same as temp file
        typer.previous_text = ""
        typer.typing_calls.clear()
        
        print(f"Temp file content: 'This is completely different content.'")
        print(f"Typed text: '{typer.typed_text[:50]}...'")
        print(f"Current text: '{typer.current_text[:50]}...'")
        
        typer.process_text_changes()
        
        if typer.typing_calls:
            print(f"✗ UNEXPECTED: Duplicate content was typed - '{typer.typing_calls[0][:50]}...'")
        else:
            print(f"✓ SUCCESS: Duplicate content was correctly skipped")
    
    finally:
        if os.path.exists(temp_path):
            os.remove(temp_path)
    
    print("\n=== Test 4: New content with temp file (should type) ===")
    # Create a temp file with different content
    temp_fd, temp_path = tempfile.mkstemp(prefix='autotyper_test_', suffix='.txt')
    typer.temp_file_path = temp_path
    typer.temp_file_created = True
    
    try:
        with os.fdopen(temp_fd, 'w') as f:
            f.write("Some old content that was typed before.")
        
        typer.typed_text = "Hello, this is the first paragraph."
        typer.current_text = "This is brand new content that should be typed."
        typer.previous_text = ""
        typer.typing_calls.clear()
        
        print(f"Temp file content: 'Some old content that was typed before.'")
        print(f"Typed text: '{typer.typed_text[:50]}...'")
        print(f"Current text: '{typer.current_text[:50]}...'")
        
        typer.process_text_changes()
        
        if typer.typing_calls:
            print(f"✓ SUCCESS: New content was typed - '{typer.typing_calls[0][:50]}...'")
        else:
            print(f"✗ FAILURE: New content was incorrectly skipped")
    
    finally:
        if os.path.exists(temp_path):
            os.remove(temp_path)

if __name__ == "__main__":
    print("Autotyper Current Behavior Test")
    print("="*60)
    
    try:
        test_current_typing_behavior()
        
        print("\n" + "="*60)
        print("TEST COMPLETE")
        print("="*60)
        
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
