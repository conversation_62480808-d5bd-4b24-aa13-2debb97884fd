#!/usr/bin/env python3
"""
Test script to verify that the word wrapping fixes handle the specific cases mentioned:
- "yahoo" being processed as "yah oo" 
- "closet" being processed as "c loset"
"""

import sys
import os

# Add the current directory to the path so we can import main
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_specific_word_wrapping_cases():
    """Test the specific cases that were failing."""
    print("Testing specific word wrapping fixes...")
    
    from main import ScreenTextTyper
    
    class TestTyper(ScreenTextTyper):
        def __init__(self):
            # Initialize required attributes for testing
            self.cursor_removal_enabled = True
            self.spell_checker = None  # We'll rely on built-in dictionaries
    
    app = TestTyper()
    
    # Test cases that were failing
    test_cases = [
        # The specific cases mentioned in the issue
        {
            'input': 'yah\noo',
            'expected': 'yahoo',
            'description': 'yahoo split as "yah oo"'
        },
        {
            'input': 'c\nloset',
            'expected': 'closet',
            'description': 'closet split as "c loset"'
        },
        
        # Additional similar cases to test the general fix
        {
            'input': 'go\nogle',
            'expected': 'google',
            'description': 'google split as "go ogle"'
        },
        {
            'input': 'po\ncket',
            'expected': 'pocket',
            'description': 'pocket split as "po cket"'
        },
        {
            'input': 'mar\nket',
            'expected': 'market',
            'description': 'market split as "mar ket"'
        },
        {
            'input': 'ti\ncket',
            'expected': 'ticket',
            'description': 'ticket split as "ti cket"'
        },
        
        # Test cases that should NOT be joined (to ensure we don't break existing functionality)
        {
            'input': 'hello\nworld',
            'expected': 'hello world',
            'description': 'hello world should remain separate'
        },
        {
            'input': 'the\nhouse',
            'expected': 'the house',
            'description': 'the house should remain separate'
        },
    ]
    
    print(f"Running {len(test_cases)} specific word wrapping tests...\n")
    
    passed = 0
    failed = 0
    
    for i, test_case in enumerate(test_cases, 1):
        try:
            input_text = test_case['input']
            expected = test_case['expected']
            description = test_case['description']
            
            # Test the normalization
            result = app.normalize_text(input_text)
            
            if result == expected:
                print(f"✅ Test {i}: {description}")
                print(f"   Input: '{input_text}' -> Output: '{result}'")
                passed += 1
            else:
                print(f"❌ Test {i}: {description}")
                print(f"   Input: '{input_text}' -> Expected: '{expected}' -> Got: '{result}'")
                failed += 1
                
        except Exception as e:
            print(f"💥 Test {i}: {description} - ERROR: {e}")
            failed += 1
        
        print()  # Add blank line between tests
    
    print(f"Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All specific word wrapping tests passed!")
        return True
    else:
        print(f"⚠️  {failed} tests failed")
        return False

def test_word_boundary_detection():
    """Test the enhanced word boundary detection logic."""
    print("\nTesting enhanced word boundary detection...")
    
    from main import ScreenTextTyper
    
    class TestTyper(ScreenTextTyper):
        def __init__(self):
            # Initialize required attributes for testing
            self.cursor_removal_enabled = True
            self.spell_checker = None  # We'll rely on built-in dictionaries
    
    app = TestTyper()
    
    # Test cases for the enhanced is_word_wrapped function
    boundary_test_cases = [
        # Should be detected as wrapped (join without space)
        ('yah', 'oo', True, 'yahoo fragments'),
        ('c', 'loset', True, 'closet fragments'),
        ('go', 'ogle', True, 'google fragments'),
        ('po', 'cket', True, 'pocket fragments'),
        ('mar', 'ket', True, 'market fragments'),
        ('ti', 'cket', True, 'ticket fragments'),
        
        # Should NOT be detected as wrapped (keep separate)
        ('hello', 'world', False, 'hello world - separate words'),
        ('the', 'house', False, 'the house - separate words'),
        ('good', 'morning', False, 'good morning - separate words'),
        ('a', 'car', False, 'a car - article + noun'),
    ]
    
    print(f"Running {len(boundary_test_cases)} word boundary detection tests...\n")
    
    passed = 0
    failed = 0
    
    for i, (prev_line, current_line, expected, description) in enumerate(boundary_test_cases, 1):
        try:
            result = app.is_word_wrapped_with_validation(prev_line, current_line)
            
            if result == expected:
                print(f"✅ Boundary Test {i}: {description}")
                print(f"   '{prev_line}' + '{current_line}' = {result}")
                passed += 1
            else:
                print(f"❌ Boundary Test {i}: {description}")
                print(f"   '{prev_line}' + '{current_line}' = {result} (expected {expected})")
                failed += 1
                
        except Exception as e:
            print(f"💥 Boundary Test {i}: {description} - ERROR: {e}")
            failed += 1
    
    print(f"\nBoundary Detection Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All word boundary detection tests passed!")
        return True
    else:
        print(f"⚠️  {failed} boundary detection tests failed")
        return False

if __name__ == "__main__":
    try:
        success1 = test_specific_word_wrapping_cases()
        success2 = test_word_boundary_detection()
        
        if success1 and success2:
            print("\n🎉 All word wrapping fix tests passed! The OCR text normalization should now correctly handle wrapped words.")
            sys.exit(0)
        else:
            print("\n❌ Some tests failed. Please check the implementation.")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n💥 Test suite failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
